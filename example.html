<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>CozyWish – Venue Listing Sample</title>

    <!-- Bootstrap 5.3+ (CSS & Icons) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css" rel="stylesheet" />

    <style>
        /* Minimal custom tweaks */
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            max-height: 100vh;
            overflow-y: auto;
        }
        .card-venue ul li + li {
            border-top: 1px solid #e9ecef;
        }
        .price-old {
            text-decoration: line-through;
        }
        .price-new {
            color: #c82333;
            font-weight: 600;
        }
    </style>
</head>
<body>
<div class="container-fluid py-4">
    <div class="row g-4">
        <!-- FILTER SIDEBAR -->
        <aside class="col-lg-3">
            <div class="sidebar bg-white border rounded p-3 shadow-sm">
                <h6 class="fw-bold mb-3">FILTER SEARCH RESULTS</h6>
                <div class="form-check form-switch mb-3">
                    <input class="form-check-input" type="checkbox" id="promotionsSwitch" />
                    <label class="form-check-label" for="promotionsSwitch">Only show promotions</label>
                </div>

                <h6 class="fw-bold mt-4 mb-2">SORT BY</h6>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="sortBy" id="sortRecommended" checked>
                    <label class="form-check-label" for="sortRecommended">Recommended</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="sortBy" id="sortNearMe">
                    <label class="form-check-label" for="sortNearMe">Near me</label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="radio" name="sortBy" id="sortHighLow">
                    <label class="form-check-label" for="sortHighLow">Price high to low</label>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="radio" name="sortBy" id="sortLowHigh">
                    <label class="form-check-label" for="sortLowHigh">Price low to high</label>
                </div>

                <hr>
                <h6 class="fw-bold mb-2">Area <span class="text-muted">(0)</span></h6>
                <input type="text" class="form-control form-control-sm mb-2" placeholder="Search area" />
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="areaBangKapi" />
                    <label class="form-check-label d-flex justify-content-between" for="areaBangKapi">
                        <span>Bang Kapi</span><span class="badge bg-light text-dark border">1</span>
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="areaBangKhae" />
                    <label class="form-check-label d-flex justify-content-between" for="areaBangKhae">
                        <span>Bang Khae</span><span class="badge bg-light text-dark border">1</span>
                    </label>
                </div>
                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="areaBangLem" />
                    <label class="form-check-label d-flex justify-content-between" for="areaBangLem">
                        <span>Bang Lamung</span><span class="badge bg-light text-dark border">1</span>
                    </label>
                </div>

                <hr>
                <h6 class="fw-bold mb-2">Service Type <span class="text-muted">(1)</span></h6>
                <input type="text" class="form-control form-control-sm mb-2" placeholder="Search service" />
                <div class="accordion" id="serviceTypeAcc">
                    <div class="accordion-item border-0">
                        <h2 class="accordion-header" id="headingAct">
                            <button class="accordion-button p-2 pt-1 collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseAct" aria-expanded="false" aria-controls="collapseAct">
                                Activities
                            </button>
                        </h2>
                        <div id="collapseAct" class="accordion-collapse collapse" aria-labelledby="headingAct" data-bs-parent="#serviceTypeAcc">
                            <div class="accordion-body py-1">
                                <div class="form-check mb-1">
                                    <input class="form-check-input" type="checkbox" id="srvAcupressure" checked>
                                    <label class="form-check-label" for="srvAcupressure">Acupressure</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>

        <!-- VENUE LIST -->
        <section class="col-lg-9">
            <!-- Venue Card 1 -->
            <div class="card card-venue mb-4 shadow-sm">
                <div class="row g-0">
                    <div class="col-md-4">
                        <img src="https://via.placeholder.com/600x400?text=Venue+Image" class="img-fluid rounded-start" alt="SKD Spa" />
                    </div>
                    <div class="col-md-8">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <h5 class="card-title mb-0">SKD Spa &amp; Massage</h5>
                                <span class="badge bg-warning text-dark"><i class="bi bi-percent"></i> PROMOTIONS</span>
                            </div>
                            <p class="card-text small text-muted mb-1"><i class="bi bi-geo-alt"></i> Bang Lamung</p>

                            <p class="mb-2">
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                            </p>

                            <ul class="list-unstyled mb-0">
                                <li class="d-flex align-items-center py-2">
                                    <div class="flex-grow-1">
                                        <strong>Nerve Touch Massage (Jab Sen)</strong>  &bull; 60 min
                                    </div>
                                    <div class="text-end">
                                        <span class="price-old">$1,290</span>
                                        <span class="price-new ms-1">$1,229</span>
                                        <button class="btn btn-sm btn-outline-secondary ms-2"><i class="bi bi-cart-plus"></i></button>
                                        <button class="btn btn-sm btn-success ms-1">Buy eVoucher</button>
                                    </div>
                                </li>
                                <li class="d-flex align-items-center py-2">
                                    <div class="flex-grow-1">
                                        <strong>Nerve Touch Massage (Jab Sen)</strong>  &bull; 90 min
                                    </div>
                                    <div class="text-end">
                                        <span class="price-old">$1,613</span>
                                        <span class="price-new ms-1">$1,529</span>
                                        <button class="btn btn-sm btn-outline-secondary ms-2"><i class="bi bi-cart-plus"></i></button>
                                        <button class="btn btn-sm btn-success ms-1">Buy eVoucher</button>
                                    </div>
                                </li>
                                <li class="d-flex align-items-center py-2">
                                    <div class="flex-grow-1">
                                        <strong>Nerve Touch Massage (Jab Sen)</strong>  &bull; 120 min
                                    </div>
                                    <div class="text-end">
                                        <span class="price-old">$1,935</span>
                                        <span class="price-new ms-1">$1,839</span>
                                        <button class="btn btn-sm btn-outline-secondary ms-2"><i class="bi bi-cart-plus"></i></button>
                                        <button class="btn btn-sm btn-success ms-1">Buy eVoucher</button>
                                    </div>
                                </li>
                            </ul>
                            <a href="#" class="small link-primary d-block mt-2">Show more services</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Venue Card 2 -->
            <div class="card card-venue mb-4 shadow-sm">
                <div class="row g-0">
                    <div class="col-md-4">
                        <img src="https://via.placeholder.com/600x400?text=Venue+Image" class="img-fluid rounded-start" alt="Vital Medi Clinic" />
                    </div>
                    <div class="col-md-8">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-center mb-1">
                                <h5 class="card-title mb-0">Vital Medi Clinic (Marche' Thonglor)</h5>
                            </div>
                            <p class="card-text small text-muted mb-1"><i class="bi bi-geo-alt"></i> Thong Lo</p>
                            <p class="mb-2">
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                                <i class="bi bi-star-fill text-warning"></i>
                            </p>

                            <ul class="list-unstyled mb-0">
                                <li class="d-flex align-items-center py-2">
                                    <div class="flex-grow-1">
                                        <strong>Acupressure with Herbal Massage Oil</strong>  &bull; 60 min
                                    </div>
                                    <div class="text-end">
                                        <span class="price-old">$1,600</span>
                                        <span class="price-new ms-1">$1,290</span>
                                        <button class="btn btn-sm btn-outline-secondary ms-2"><i class="bi bi-cart-plus"></i></button>
                                        <button class="btn btn-sm btn-success ms-1">Buy eVoucher</button>
                                    </div>
                                </li>
                                <li class="d-flex align-items-center py-2">
                                    <div class="flex-grow-1">
                                        <strong>Acupressure with Herbal Massage Oil &amp; Herbal Compress</strong>  &bull; 60 min
                                    </div>
                                    <div class="text-end">
                                        <span class="price-old">$1,790</span>
                                        <span class="price-new ms-1">$1,390</span>
                                        <button class="btn btn-sm btn-outline-secondary ms-2"><i class="bi bi-cart-plus"></i></button>
                                        <button class="btn btn-sm btn-success ms-1">Buy eVoucher</button>
                                    </div>
                                </li>
                            </ul>
                            <a href="#" class="small link-primary d-block mt-2">Show more services</a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<!-- Bootstrap JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
