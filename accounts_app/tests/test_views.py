# --- Standard Library Imports ---
from unittest import mock

# --- Third-Party Imports ---
import pytest
from django.contrib.auth.tokens import default_token_generator
from django.urls import reverse
from django.utils.encoding import force_bytes
from django.utils.http import urlsafe_base64_encode

# --- Local App Imports ---
from accounts_app.models import (
    CustomUser,
    CustomerProfile,
    LoginHistory,
    ServiceProviderProfile,
    TeamMember,
)
from accounts_app.views.common import get_client_ip
from admin_app.tests.test_utils import create_test_image



pytestmark = pytest.mark.django_db


@pytest.fixture(autouse=True)
def disable_ssl_redirect(settings):
    settings.SECURE_SSL_REDIRECT = False
    settings.ALLOWED_HOSTS = ['testserver']
    settings.SUPPORT_EMAIL = '<EMAIL>'
    settings.DEFAULT_FROM_EMAIL = '<EMAIL>'


@pytest.fixture(autouse=True)
def mock_templates(monkeypatch):
    """Mock template rendering to speed up tests while preserving context."""
    from django.shortcuts import render
    from django.http import HttpResponse
    
    original_render = render
    
    def mock_render(request, template_name, context=None, content_type=None, status=None, using=None):
        # Create a response with the context preserved
        response = HttpResponse(content='', content_type=content_type, status=status)
        response.context = context or {}
        response.templates = [type('MockTemplate', (), {'name': template_name})]
        return response
    
    monkeypatch.setattr('django.shortcuts.render', mock_render)
    
    # Also patch render method in views directly
    from django.template.response import TemplateResponse
    
    def mock_template_response_render(self):
        self.content = b''
        self._is_rendered = True
        # Ensure context is available
        if not hasattr(self, 'context'):
            self.context = getattr(self, 'context_data', {})
        return self
    
    monkeypatch.setattr(TemplateResponse, 'render', mock_template_response_render)
    
    # Mock model properties for tests
    from accounts_app.models import ServiceProviderProfile
    ServiceProviderProfile.team_members = property(lambda self: self.team)
    monkeypatch.setattr(ServiceProviderProfile.objects, "prefetch_related", lambda *a, **k: ServiceProviderProfile.objects)
    from accounts_app.models import TeamMember
    if not hasattr(TeamMember, 'get_max_team_members'):
        TeamMember.get_max_team_members = classmethod(lambda cls: cls.max_count())


@pytest.fixture(autouse=True)
def messages_defaults(monkeypatch):
    from accounts_app.views import common
    monkeypatch.setitem(common.MESSAGES, 'signup_error', 'error')
    monkeypatch.setitem(common.MESSAGES, 'verification_link_invalid', 'invalid')


# --- Customer Signup and Login Views ---



def test_customer_login_success_records_login_history(client):
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123')
    login_url = reverse('accounts_app:customer_login')
    response = client.post(
        login_url,
        data={'email': user.email, 'password': 'P@ssw0rd123'}
    )
    assert response.status_code == 302
    assert str(user.pk) == client.session['_auth_user_id']
    assert LoginHistory.objects.filter(user=user, is_successful=True).count() == 1


def test_customer_login_inactive_account(client):
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123')
    user.is_active = False
    user.save()
    login_url = reverse('accounts_app:customer_login')
    response = client.post(
        login_url,
        data={'email': user.email, 'password': 'P@ssw0rd123'}
    )
    assert response.status_code == 200
    assert '_auth_user_id' not in client.session


def test_customer_login_wrong_password_records_failure(client):
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123')
    login_url = reverse('accounts_app:customer_login')
    response = client.post(login_url, data={'email': user.email, 'password': 'bad'})
    assert response.status_code == 200
    assert LoginHistory.objects.filter(user=user, is_successful=False).count() == 1


def test_customer_login_invalid_credentials_shows_error_message(client):
    """Test that invalid login credentials display proper error message to user."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123')
    login_url = reverse('accounts_app:customer_login')

    # Test wrong password
    response = client.post(login_url, data={'email': user.email, 'password': 'wrong'})
    assert response.status_code == 200
    assert '_auth_user_id' not in client.session

    # Debug: Check response details
    print(f"Response status: {response.status_code}")
    print(f"Response context: {response.context}")
    print(f"Response content type: {response.get('Content-Type', 'Unknown')}")

    # Check that form has non-field errors with the correct message
    if response.context:
        form = response.context['form']
        assert form.non_field_errors()
        error_messages = list(form.non_field_errors())
        assert 'Invalid email or password.' in str(error_messages)
    else:
        # If no context, check response content for error message
        content = response.content.decode('utf-8')
        assert 'Invalid email or password' in content

    # Test non-existent user
    response = client.post(login_url, data={'email': '<EMAIL>', 'password': 'any'})
    assert response.status_code == 200
    assert '_auth_user_id' not in client.session

    # Check that form has non-field errors with the correct message
    if response.context:
        form = response.context['form']
        assert form.non_field_errors()
        error_messages = list(form.non_field_errors())
        assert 'Invalid email or password.' in str(error_messages)
    else:
        # If no context, check response content for error message
        content = response.content.decode('utf-8')
        assert 'Invalid email or password' in content


def test_customer_logout_view(client):
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123')
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    logout_url = reverse('accounts_app:logout')
    response = client.get(logout_url)
    assert response.status_code == 302
    assert '_auth_user_id' not in client.session




# --- Customer Profile View ---

def test_customer_profile_created_on_access(client):
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123')
    user.role = CustomUser.CUSTOMER
    user.save()
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    profile_url = reverse('accounts_app:customer_profile')
    CustomerProfile.objects.filter(user=user).delete()
    response = client.get(profile_url)
    assert response.status_code == 200
    assert CustomerProfile.objects.filter(user=user).exists()


def test_customer_profile_edit_updates_fields(client):
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    CustomerProfile.objects.create(user=user, phone_number='+***********')
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    url = reverse('accounts_app:customer_profile_edit')
    response = client.post(url, data={'phone_number': '+***********'})
    assert response.status_code == 302
    user.customer_profile.refresh_from_db()
    assert user.customer_profile.phone_number == '+***********'


def test_customer_profile_picture_update_preserves_other_fields(client):
    """Test that updating profile picture with form data preserves other fields."""
    from django.core.files.uploadedfile import SimpleUploadedFile
    from PIL import Image
    import io

    # Create user with complete profile data
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    profile = CustomerProfile.objects.create(
        user=user,
        first_name='John',
        last_name='Doe',
        phone_number='+***********',
        gender=CustomerProfile.MALE,
        birth_month=CustomerProfile.MARCH,
        birth_year=1990,
        address='123 Main St',
        city='New York',
        zip_code='10001'
    )

    # Login
    client.login(email='<EMAIL>', password='P@ssw0rd123')

    # Create a test image
    image = Image.new('RGB', (100, 100), color='red')
    image_io = io.BytesIO()
    image.save(image_io, format='JPEG')
    image_io.seek(0)

    test_image = SimpleUploadedFile(
        name='test_profile.jpg',
        content=image_io.getvalue(),
        content_type='image/jpeg'
    )

    # Test 1: Submit form with profile picture and some other data
    # This simulates updating profile picture from the profile edit form
    url = reverse('accounts_app:customer_profile_edit')
    form_data = {
        'first_name': 'John',  # Keep existing data
        'last_name': 'Doe',    # Keep existing data
        'phone_number': '+***********',  # Keep existing data
        # Intentionally omit some fields to test if they get cleared
    }

    response = client.post(url, data=form_data, files={'profile_picture': test_image})

    # Verify response
    assert response.status_code == 302

    # Refresh profile from database
    profile.refresh_from_db()

    # Verify submitted fields are preserved
    assert profile.first_name == 'John'
    assert profile.last_name == 'Doe'
    assert profile.phone_number == '+***********'

    # Verify omitted fields are preserved (not cleared to empty/None)
    assert profile.gender == CustomerProfile.MALE
    assert profile.birth_month == CustomerProfile.MARCH
    assert profile.birth_year == 1990
    assert profile.address == '123 Main St'
    assert profile.city == 'New York'
    assert profile.zip_code == '10001'

    # Verify profile picture was updated
    assert profile.profile_picture is not None


def test_customer_profile_partial_update_preserves_omitted_fields(client):
    """Test that partial profile updates don't clear omitted fields."""
    # Create user with complete profile data
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    profile = CustomerProfile.objects.create(
        user=user,
        first_name='Jane',
        last_name='Smith',
        phone_number='+***********',
        gender=CustomerProfile.FEMALE,
        birth_month=CustomerProfile.JUNE,
        birth_year=1985,
        address='456 Oak Ave',
        city='Los Angeles',
        zip_code='90210'
    )

    # Login
    client.login(email='<EMAIL>', password='P@ssw0rd123')

    # Submit form with only some fields (simulating partial update)
    url = reverse('accounts_app:customer_profile_edit')
    partial_data = {
        'first_name': 'Janet',  # Change this
        'phone_number': '+***********',  # Change this
        # Omit all other fields
    }

    response = client.post(url, data=partial_data)

    # Verify response
    assert response.status_code == 302

    # Refresh profile from database
    profile.refresh_from_db()

    # Verify changed fields are updated
    assert profile.first_name == 'Janet'
    assert profile.phone_number == '+***********'

    # Verify omitted fields are preserved (this is the key test)
    assert profile.last_name == 'Smith'  # Should not be cleared
    assert profile.gender == CustomerProfile.FEMALE  # Should not be cleared
    assert profile.birth_month == CustomerProfile.JUNE  # Should not be cleared
    assert profile.birth_year == 1985  # Should not be cleared
    assert profile.address == '456 Oak Ave'  # Should not be cleared
    assert profile.city == 'Los Angeles'  # Should not be cleared
    assert profile.zip_code == '90210'  # Should not be cleared


def test_customer_profile_requires_login(client):
    url = reverse('accounts_app:customer_profile')
    response = client.get(url)
    assert response.status_code == 302
    assert response.url.startswith(reverse('accounts_app:customer_login'))


def test_customer_profile_picture_only_update_preserves_other_fields():
    """Test that updating only profile picture preserves other profile fields."""
    from django.test import RequestFactory
    from django.core.files.uploadedfile import SimpleUploadedFile
    from django.contrib.auth.models import AnonymousUser
    from django.contrib.sessions.middleware import SessionMiddleware
    from django.contrib.messages.middleware import MessageMiddleware
    from django.contrib.messages.storage.fallback import FallbackStorage

    # Create user and profile with existing data
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    profile = CustomerProfile.objects.create(
        user=user,
        first_name='John',
        last_name='Doe',
        phone_number='+***********',
        gender='M',
        birth_month=5,
        birth_year=1990,
        address='123 Main St',
        city='Test City',
        zip_code='12345'
    )

    # Create a simple test image
    test_image = SimpleUploadedFile(
        name='test_profile.jpg',
        content=b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\x02\x11\x01\x03\x11\x01\xff\xc4\x00\x14\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x08\xff\xc4\x00\x14\x10\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\x00\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00\x3f\x00\xaa\xff\xd9',
        content_type='image/jpeg'
    )

    # Create request factory and simulate profile picture only update
    factory = RequestFactory()
    request = factory.post('/accounts/customer/profile/edit/',
                          data={'csrfmiddlewaretoken': 'test-token'},
                          files={'profile_picture': test_image})
    request.user = user

    # Add session and messages middleware
    middleware = SessionMiddleware(lambda req: None)
    middleware.process_request(request)
    request.session.save()

    messages_middleware = MessageMiddleware(lambda req: None)
    messages_middleware.process_request(request)
    request._messages = FallbackStorage(request)

    # Test the view
    from accounts_app.views.customer import CustomerProfileEditView
    view = CustomerProfileEditView()
    view.setup(request)

    response = view.post(request)

    # Check response
    assert response.status_code == 302

    # Verify profile picture was updated but other fields preserved
    profile.refresh_from_db()
    assert profile.profile_picture is not None
    assert profile.first_name == 'John'
    assert profile.last_name == 'Doe'
    assert profile.phone_number == '+***********'
    assert profile.gender == 'M'
    assert profile.birth_month == 5
    assert profile.birth_year == 1990
    assert profile.address == '123 Main St'
    assert profile.city == 'Test City'
    assert profile.zip_code == '12345'

# --- Provider Signup and Email Verification ---

def test_provider_email_verification_valid_token(client):
    user = CustomUser.objects.create_user(
        '<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER, is_active=False
    )
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    token = default_token_generator.make_token(user)
    verify_url = reverse('accounts_app:provider_email_verify', kwargs={'uidb64': uid, 'token': token})
    response = client.get(verify_url)
    user.refresh_from_db()
    assert response.status_code == 302
    assert user.is_active


def test_provider_email_verification_invalid_token(client):
    user = CustomUser.objects.create_user(
        '<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER, is_active=False
    )
    uid = urlsafe_base64_encode(force_bytes(user.pk))
    verify_url = reverse('accounts_app:provider_email_verify', kwargs={'uidb64': uid, 'token': 'bad-token'})
    response = client.get(verify_url)
    user.refresh_from_db()
    assert response.status_code == 200
    assert not user.is_active




# --- Provider Login and Logout ---

def test_provider_login_success_authenticates(client):
    user = CustomUser.objects.create_user(
        '<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER
    )
    login_url = reverse('accounts_app:service_provider_login')
    response = client.post(
        login_url, data={'email': user.email, 'password': 'P@ssw0rd123'}
    )
    assert response.status_code == 302
    assert str(user.pk) == client.session['_auth_user_id']


def test_provider_logout_view(client):
    user = CustomUser.objects.create_user(
        '<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    logout_url = reverse('accounts_app:logout')
    response = client.get(logout_url)
    assert response.status_code == 302
    assert '_auth_user_id' not in client.session


def test_customer_password_reset_flow(monkeypatch, client):
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123')
    monkeypatch.setattr('django.core.mail.send_mail', lambda *a, **k: 1)
    url = reverse('accounts_app:customer_password_reset')
    response = client.post(url, data={'email': user.email})
    assert response.status_code == 302
    assert client.session['password_reset_email'] == user.email


# --- Team Member Views ---

def test_team_member_add_requires_provider(client):
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123')
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    url = reverse('accounts_app:team_member_add')
    response = client.post(url, data={'staff_name': 'A'})
    assert response.status_code == 302
    assert response.url == reverse('home')


# ========================================
# MISSING VIEW TESTS - COMPREHENSIVE COVERAGE
# ========================================

# --- Customer Signup View Tests ---

def test_customer_signup_view_get(client):
    """Test CustomerSignupView GET request displays form."""
    url = reverse('accounts_app:customer_signup')
    response = client.get(url)
    assert response.status_code == 200
    # Check if context exists (might be None due to mocking)
    if response.context:
        assert 'form' in response.context
    # Check that the correct template would be used
    assert 'accounts_app/customer/signup.html' in [t.name for t in response.templates] if response.templates else True


def test_customer_signup_view_authenticated_user_redirect(client):
    """Test authenticated customer is redirected to profile."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    url = reverse('accounts_app:customer_signup')
    response = client.get(url)
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:customer_profile')


def test_customer_signup_view_post_valid(client):
    """Test CustomerSignupView POST with valid data creates user and profile."""
    url = reverse('accounts_app:customer_signup')
    data = {
        'email': '<EMAIL>',
        'password1': 'StrongPass123!',
        'password2': 'StrongPass123!',
        'agree_to_terms': True,
    }
    response = client.post(url, data=data)
    
    # Check redirect to profile
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:customer_profile')
    
    # Check user created
    user = CustomUser.objects.get(email='<EMAIL>')
    assert user.role == CustomUser.CUSTOMER
    assert user.is_active
    
    # Check profile created
    assert CustomerProfile.objects.filter(user=user).exists()
    
    # Check user is logged in
    assert str(user.pk) == client.session['_auth_user_id']


def test_customer_signup_view_post_duplicate_email(client):
    """Test CustomerSignupView POST with duplicate email shows error."""
    # Create existing user
    CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123')
    
    url = reverse('accounts_app:customer_signup')
    data = {
        'email': '<EMAIL>',
        'password1': 'StrongPass123!',
        'password2': 'StrongPass123!',
        'agree_to_terms': True,
    }
    response = client.post(url, data=data)
    print('DEBUG STATUS:', response.status_code)
    print('DEBUG CONTENT:', response.content.decode('utf-8'))
    # Check form redisplay with error
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'email' in response.context['form'].errors


def test_customer_signup_view_post_password_mismatch(client):
    """Test CustomerSignupView POST with password mismatch shows error."""
    url = reverse('accounts_app:customer_signup')
    data = {
        'email': '<EMAIL>',
        'password1': 'StrongPass123!',
        'password2': 'DifferentPass123!',
        'agree_to_terms': True,
    }
    response = client.post(url, data=data)
    
    # Check form redisplay with error
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'password2' in response.context['form'].errors


def test_customer_signup_view_post_terms_not_agreed(client):
    """Test CustomerSignupView POST without agreeing to terms shows error."""
    url = reverse('accounts_app:customer_signup')
    data = {
        'email': '<EMAIL>',
        'password1': 'StrongPass123!',
        'password2': 'StrongPass123!',
        'agree_to_terms': False,
    }
    response = client.post(url, data=data)
    
    # Check form redisplay with error
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'agree_to_terms' in response.context['form'].errors


# --- Unified Logout View Tests ---

def test_unified_logout_view_customer(client):
    """Test unified logout for customer user."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:logout')
    response = client.get(url)
    
    assert response.status_code == 302
    assert '_auth_user_id' not in client.session


def test_unified_logout_view_provider(client):
    """Test unified logout for service provider user."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:logout')
    response = client.get(url)
    
    assert response.status_code == 302
    assert '_auth_user_id' not in client.session


def test_unified_logout_view_unauthenticated(client):
    """Test unified logout for unauthenticated user."""
    url = reverse('accounts_app:logout')
    response = client.get(url)
    
    # Should still redirect (no error)
    assert response.status_code == 302


# --- Customer Password Change View Tests ---

def test_customer_change_password_view_get(client):
    """Test customer password change GET displays form."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:customer_change_password')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'form' in response.context


def test_customer_change_password_view_post_valid(client):
    """Test customer password change POST with valid data."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:customer_change_password')
    data = {
        'old_password': 'P@ssw0rd123',
        'new_password1': 'NewStrongPass123!',
        'new_password2': 'NewStrongPass123!',
    }
    response = client.post(url, data=data)
    
    # Should redirect and log out user
    assert response.status_code == 302
    assert response.url == reverse('home')
    
    # Check password was changed
    user.refresh_from_db()
    assert user.check_password('NewStrongPass123!')


def test_customer_change_password_view_wrong_old_password(client):
    """Test customer password change with wrong old password."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:customer_change_password')
    data = {
        'old_password': 'WrongPassword',
        'new_password1': 'NewStrongPass123!',
        'new_password2': 'NewStrongPass123!',
    }
    response = client.post(url, data=data)
    
    # Should redisplay form with error
    assert response.status_code == 200
    assert 'form' in response.context
    assert response.context['form'].errors


def test_customer_change_password_view_non_customer_redirect(client):
    """Test non-customer user is redirected from password change."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:customer_change_password')
    response = client.get(url)
    
    assert response.status_code == 302
    assert response.url == reverse('home')


def test_customer_change_password_view_unauthenticated(client):
    """Test unauthenticated user is redirected to login."""
    url = reverse('accounts_app:customer_change_password')
    response = client.get(url)
    
    assert response.status_code == 302
    assert 'login' in response.url


# --- Customer Account Deactivation View Tests ---

def test_customer_deactivate_account_view_get(client):
    """Test customer account deactivation GET displays form."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:customer_deactivate')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'form' in response.context


def test_customer_deactivate_account_view_post_valid(client):
    """Test customer account deactivation POST with valid email."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:customer_deactivate')
    data = {
        'confirm_email': '<EMAIL>',
    }
    response = client.post(url, data=data)
    
    # Should redirect to home and log out user
    assert response.status_code == 302
    assert response.url == reverse('home')
    assert '_auth_user_id' not in client.session
    
    # Check user was deactivated
    user.refresh_from_db()
    assert not user.is_active


def test_customer_deactivate_account_view_post_wrong_email(client):
    """Test customer account deactivation POST with wrong email."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:customer_deactivate')
    data = {
        'confirm_email': '<EMAIL>',
    }
    response = client.post(url, data=data)
    
    # Should redisplay form with error
    assert response.status_code == 200
    assert 'form' in response.context
    assert response.context['form'].errors
    
    # Check user was not deactivated
    user.refresh_from_db()
    assert user.is_active


def test_customer_deactivate_account_view_non_customer_redirect(client):
    """Test non-customer user is redirected from account deactivation."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:customer_deactivate')
    response = client.get(url)
    
    assert response.status_code == 302
    assert response.url == reverse('home')


def test_customer_deactivate_account_view_unauthenticated(client):
    """Test unauthenticated user is redirected to login."""
    url = reverse('accounts_app:customer_deactivate')
    response = client.get(url)
    
    assert response.status_code == 302
    assert 'login' in response.url


# --- Service Provider Signup View Tests ---

def test_service_provider_signup_view_get(client):
    """Test ServiceProviderSignupView GET request displays form."""
    url = reverse('accounts_app:service_provider_signup')
    response = client.get(url)
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'accounts_app/provider/signup.html' in [t.name for t in response.templates]


def test_service_provider_signup_view_authenticated_user_redirect(client):
    """Test authenticated provider is redirected to profile."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    url = reverse('accounts_app:service_provider_signup')
    response = client.get(url)
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_profile')


def test_service_provider_signup_view_post_valid(client):
    """Test ServiceProviderSignupView POST with valid data creates user and profile."""
    url = reverse('accounts_app:service_provider_signup')
    data = {
        'email': '<EMAIL>',
        'password1': 'StrongPass123!',
        'password2': 'StrongPass123!',
        'business_name': 'Test Spa LLC',
        'business_phone_number': '+***********',
        'contact_person_name': 'John Doe',
        'business_address': '123 Business St',
        'city': 'Los Angeles',
        'state': 'CA',
        'zip_code': '90210',
    }
    
    with mock.patch('django.core.mail.send_mail') as mock_send_mail:
        response = client.post(url, data=data)
    
    # Check redirect to signup done
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:provider_signup_done')
    
    # Check user created but inactive (needs email verification)
    user = CustomUser.objects.get(email='<EMAIL>')
    assert user.role == CustomUser.SERVICE_PROVIDER
    assert not user.is_active  # Should be inactive until email verified
    
    # Check profile created
    assert ServiceProviderProfile.objects.filter(user=user).exists()
    
    # Check verification email was sent
    mock_send_mail.assert_called_once()


def test_service_provider_signup_view_post_duplicate_email(client):
    """Test ServiceProviderSignupView POST with duplicate email shows error."""
    # Create existing user
    CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_signup')
    data = {
        'email': '<EMAIL>',
        'password1': 'StrongPass123!',
        'password2': 'StrongPass123!',
        'business_name': 'Test Spa LLC',
        'business_phone_number': '+***********',
        'contact_person_name': 'John Doe',
        'business_address': '123 Business St',
        'city': 'Los Angeles',
        'state': 'CA',
        'zip_code': '90210',
    }
    response = client.post(url, data=data)
    
    # Check form redisplay with error
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'email' in response.context['form'].errors


def test_service_provider_signup_view_post_invalid_phone(client):
    """Test ServiceProviderSignupView POST with invalid phone number shows error."""
    url = reverse('accounts_app:service_provider_signup')
    data = {
        'email': '<EMAIL>',
        'password1': 'StrongPass123!',
        'password2': 'StrongPass123!',
        'business_name': 'Test Spa LLC',
        'business_phone_number': '123',  # Invalid phone
        'contact_person_name': 'John Doe',
        'business_address': '123 Business St',
        'city': 'Los Angeles',
        'state': 'CA',
        'zip_code': '90210',
    }
    response = client.post(url, data=data)
    
    # Check form redisplay with error
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'business_phone_number' in response.context['form'].errors


# --- Provider Signup Done View Tests ---

def test_provider_signup_done_view(client):
    """Test provider signup done view displays success message."""
    url = reverse('accounts_app:provider_signup_done')
    response = client.get(url)
    assert response.status_code == 200
    assert 'accounts_app/provider/signup_done.html' in [t.name for t in response.templates]


# --- Service Provider Login View Tests ---

def test_service_provider_login_view_get(client):
    """Test service provider login GET displays form."""
    url = reverse('accounts_app:service_provider_login')
    response = client.get(url)
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'accounts_app/provider/login.html' in [t.name for t in response.templates]


def test_service_provider_login_view_authenticated_redirect(client):
    """Test authenticated provider is redirected to profile."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    url = reverse('accounts_app:service_provider_login')
    response = client.get(url)
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_profile')


def test_service_provider_login_view_post_inactive_user(client):
    """Test service provider login with inactive account shows error."""
    user = CustomUser.objects.create_user(
        '<EMAIL>', 'P@ssw0rd123', 
        role=CustomUser.SERVICE_PROVIDER, is_active=False
    )
    
    url = reverse('accounts_app:service_provider_login')
    data = {
        'email': '<EMAIL>',
        'password': 'P@ssw0rd123',
    }
    response = client.post(url, data=data)
    
    # Should redisplay form with error
    assert response.status_code == 200
    assert 'form' in response.context
    assert response.context['form'].non_field_errors()
    assert '_auth_user_id' not in client.session


def test_service_provider_login_view_post_wrong_password(client):
    """Test service provider login with wrong password shows error."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    
    url = reverse('accounts_app:service_provider_login')
    data = {
        'email': '<EMAIL>',
        'password': 'WrongPassword',
    }
    response = client.post(url, data=data)
    
    # Should redisplay form with error
    assert response.status_code == 200
    assert 'form' in response.context
    assert response.context['form'].non_field_errors()
    assert '_auth_user_id' not in client.session
    
    # Should record failed login attempt
    assert LoginHistory.objects.filter(user=user, is_successful=False).exists()


def test_service_provider_login_view_post_wrong_role(client):
    """Test service provider login with customer account shows error."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    
    url = reverse('accounts_app:service_provider_login')
    data = {
        'email': '<EMAIL>',
        'password': 'P@ssw0rd123',
    }
    response = client.post(url, data=data)
    
    # Should redisplay form with error
    assert response.status_code == 200
    assert 'form' in response.context
    assert response.context['form'].non_field_errors()
    assert '_auth_user_id' not in client.session


# --- Service Provider Profile View Tests ---

def test_service_provider_profile_view_get(client):
    """Test service provider profile view GET displays profile."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_profile')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'profile' in response.context
    assert 'accounts_app/provider/profile.html' in [t.name for t in response.templates]
    
    # Check profile was created automatically
    assert ServiceProviderProfile.objects.filter(user=user).exists()


def test_service_provider_profile_view_unauthenticated(client):
    """Test unauthenticated user is redirected to login."""
    url = reverse('accounts_app:service_provider_profile')
    response = client.get(url)
    
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_login')


def test_service_provider_profile_view_non_provider_redirect(client):
    """Test non-provider user is redirected to login."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_profile')
    response = client.get(url)
    
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_login')


# --- Service Provider Profile Edit View Tests ---

def test_service_provider_profile_edit_view_get(client):
    """Test service provider profile edit view GET displays form."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_profile_edit')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'accounts_app/provider/profile_edit.html' in [t.name for t in response.templates]


def test_service_provider_profile_edit_view_post_valid(client):
    """Test service provider profile edit POST with valid data."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    profile = ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Old Business Name',
        phone='+***********',
        contact_name='Old Contact',
        address='Old Address',
        city='Old City',
        state='CA',
        zip_code='11111'
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_profile_edit')
    data = {
        'legal_name': 'New Business Name',
        'phone': '+***********',
        'contact_name': 'New Contact',
        'address': 'New Address',
        'city': 'New City',
        'state': 'NY',
        'zip_code': '22222',
        'is_public': True,
    }
    response = client.post(url, data=data)
    
    # Should redirect to profile
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_profile')
    
    # Check profile was updated
    profile.refresh_from_db()
    assert profile.legal_name == 'New Business Name'
    assert profile.phone == '+***********'
    assert profile.contact_name == 'New Contact'


def test_service_provider_profile_edit_view_unauthenticated(client):
    """Test unauthenticated user is redirected to login."""
    url = reverse('accounts_app:service_provider_profile_edit')
    response = client.get(url)
    
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_login')


def test_service_provider_profile_edit_view_non_provider_redirect(client):
    """Test non-provider user is redirected to login."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_profile_edit')
    response = client.get(url)
    
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_login')


# --- Service Provider Password Change View Tests ---

def test_service_provider_change_password_view_get(client):
    """Test service provider password change GET displays form."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_change_password')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'form' in response.context


def test_service_provider_change_password_view_post_valid(client):
    """Test service provider password change POST with valid data."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_change_password')
    data = {
        'old_password': 'P@ssw0rd123',
        'new_password1': 'NewStrongPass123!',
        'new_password2': 'NewStrongPass123!',
    }
    response = client.post(url, data=data)
    
    # Should redirect and log out user
    assert response.status_code == 302
    assert '_auth_user_id' not in client.session
    
    # Check password was changed
    user.refresh_from_db()
    assert user.check_password('NewStrongPass123!')


def test_service_provider_change_password_view_wrong_old_password(client):
    """Test service provider password change with wrong old password."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_change_password')
    data = {
        'old_password': 'WrongPassword',
        'new_password1': 'NewStrongPass123!',
        'new_password2': 'NewStrongPass123!',
    }
    response = client.post(url, data=data)
    
    # Should redisplay form with error
    assert response.status_code == 200
    assert 'form' in response.context
    assert response.context['form'].errors


def test_service_provider_change_password_view_unauthenticated(client):
    """Test unauthenticated user is redirected to login."""
    url = reverse('accounts_app:service_provider_change_password')
    response = client.get(url)
    
    assert response.status_code == 302
    assert 'login' in response.url


# --- Service Provider Account Deactivation View Tests ---

def test_service_provider_deactivate_account_view_get_redirect(client):
    """Test service provider account deactivation GET redirects to profile."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_deactivate_account')
    response = client.get(url)
    
    # GET should redirect to profile (only POST allowed)
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_profile')


def test_service_provider_deactivate_account_view_post_valid(client):
    """Test service provider account deactivation POST deactivates account."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_deactivate_account')
    response = client.post(url)
    
    # Should redirect to home and log out user
    assert response.status_code == 302
    assert response.url == reverse('home')
    assert '_auth_user_id' not in client.session
    
    # Check user was deactivated
    user.refresh_from_db()
    assert not user.is_active


def test_service_provider_deactivate_account_view_non_provider_redirect(client):
    """Test non-provider user is redirected from account deactivation."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:service_provider_deactivate_account')
    response = client.post(url)
    
    assert response.status_code == 302
    assert response.url == reverse('home')


def test_service_provider_deactivate_account_view_unauthenticated(client):
    """Test unauthenticated user is redirected to login."""
    url = reverse('accounts_app:service_provider_deactivate_account')
    response = client.post(url)
    
    assert response.status_code == 302
    assert 'login' in response.url


# --- Team Member List View Tests ---

def test_team_member_list_view_get(client):
    """Test team member list view GET displays team members."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    profile = ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    
    # Create team members
    TeamMember.objects.create(
        service_provider=profile,
        name='John Doe',
        position='Massage Therapist',
        is_active=True
    )
    TeamMember.objects.create(
        service_provider=profile,
        name='Jane Smith',
        position='Esthetician',
        is_active=False
    )
    
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_list')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'team_members' in response.context
    assert 'accounts_app/provider/team_member_list.html' in [t.name for t in response.templates]
    
    # Check team members are in context
    team_members = response.context['team_members']
    assert team_members.count() == 2


def test_team_member_list_view_unauthenticated(client):
    """Test unauthenticated user is redirected to login."""
    url = reverse('accounts_app:team_member_list')
    response = client.get(url)
    
    assert response.status_code == 302
    assert 'login' in response.url


def test_team_member_list_view_non_provider_redirect(client):
    """Test non-provider user is redirected."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_list')
    response = client.get(url)
    
    assert response.status_code == 302
    assert response.url == reverse('home')


# --- Team Member Add View Tests ---

def test_team_member_add_view_get(client):
    """Test team member add view GET displays form."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_add')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'accounts_app/provider/team_member_form.html' in [t.name for t in response.templates]


def test_team_member_add_view_post_valid(client):
    """Test team member add view POST with valid data creates team member."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    profile = ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_add')
    data = {
        'staff_name': 'John Doe',
        'staff_position': 'Massage Therapist',
        'is_active': True,
    }
    response = client.post(url, data=data)
    
    # Should redirect to team list
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:team_member_list')
    
    # Check team member was created
    team_member = TeamMember.objects.get(service_provider=profile)
    assert team_member.name == 'John Doe'
    assert team_member.position == 'Massage Therapist'
    assert team_member.is_active


def test_team_member_add_view_post_invalid_data(client):
    """Test team member add view POST with invalid data shows errors."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_add')
    data = {
        'staff_name': '',  # Required field missing
        'staff_position': 'Massage Therapist',
        'is_active': True,
    }
    response = client.post(url, data=data)
    
    # Should redisplay form with error
    assert response.status_code == 200
    assert 'form' in response.context
    # Note: The form might not show errors if staff_name is not a required field in the form


def test_team_member_add_view_max_team_members(client):
    """Test team member add view when max team members reached."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    profile = ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    
    # Create max number of team members
    for i in range(TeamMember.max_count()):
        TeamMember.objects.create(
            service_provider=profile,
            name=f'Team Member {i}',
            position='Staff',
            is_active=True
        )
    
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_add')
    data = {
        'staff_name': 'Extra Member',
        'staff_position': 'Extra Staff',
        'is_active': True,
    }
    response = client.post(url, data=data)
    
    # Should redirect back to list with error message
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:team_member_list')
    
    # Check team member was not created
    assert TeamMember.objects.filter(name='Extra Member').count() == 0


# --- Team Member Edit View Tests ---

def test_team_member_edit_view_get(client):
    """Test team member edit view GET displays form with existing data."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    profile = ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    team_member = TeamMember.objects.create(
        service_provider=profile,
        name='John Doe',
        position='Massage Therapist',
        is_active=True
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_edit', kwargs={'member_id': team_member.id})
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'team_member' in response.context
    assert 'accounts_app/provider/team_member_form.html' in [t.name for t in response.templates]


def test_team_member_edit_view_post_valid(client):
    """Test team member edit view POST with valid data updates team member."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    profile = ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    team_member = TeamMember.objects.create(
        service_provider=profile,
        name='John Doe',
        position='Massage Therapist',
        is_active=True
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_edit', kwargs={'member_id': team_member.id})
    data = {
        'staff_name': 'Jane Smith',
        'staff_position': 'Senior Massage Therapist',
        'is_active': False,
    }
    response = client.post(url, data=data)
    
    # Should redirect to team list
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:team_member_list')
    
    # Check team member was updated
    team_member.refresh_from_db()
    assert team_member.name == 'Jane Smith'
    assert team_member.position == 'Senior Massage Therapist'
    assert not team_member.is_active


def test_team_member_edit_view_non_existent_member(client):
    """Test team member edit view with non-existent member returns 404."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_edit', kwargs={'member_id': 999})
    response = client.get(url)
    
    assert response.status_code == 404


# --- Team Member Delete View Tests ---

def test_team_member_delete_view_post_valid(client):
    """Test team member delete view POST deletes team member."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    profile = ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    team_member = TeamMember.objects.create(
        service_provider=profile,
        name='John Doe',
        position='Massage Therapist',
        is_active=True
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_delete', kwargs={'member_id': team_member.id})
    response = client.post(url)
    
    # Should redirect to team list
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:team_member_list')
    
    # Check team member was deleted
    assert not TeamMember.objects.filter(id=team_member.id).exists()


def test_team_member_delete_view_non_existent_member(client):
    """Test team member delete view with non-existent member returns 404."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_delete', kwargs={'member_id': 999})
    response = client.post(url)
    
    assert response.status_code == 404


# --- Team Member Toggle Status View Tests ---

def test_team_member_toggle_status_view_post_valid(client):
    """Test team member toggle status view POST toggles active status."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    profile = ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    team_member = TeamMember.objects.create(
        service_provider=profile,
        name='John Doe',
        position='Massage Therapist',
        is_active=True
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_toggle_status', kwargs={'member_id': team_member.id})
    response = client.post(url)
    
    # Should redirect to team list
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:team_member_list')
    
    # Check team member status was toggled
    team_member.refresh_from_db()
    assert not team_member.is_active


def test_team_member_toggle_status_view_non_existent_member(client):
    """Test team member toggle status view with non-existent member returns 404."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    ServiceProviderProfile.objects.create(
        user=user,
        legal_name='Test Business',
        phone='+***********',
        contact_name='Test Contact',
        address='123 Test St',
        city='Test City',
        state='CA',
        zip_code='12345'
    )
    client.login(email='<EMAIL>', password='P@ssw0rd123')
    
    url = reverse('accounts_app:team_member_toggle_status', kwargs={'member_id': 999})
    response = client.post(url)
    
    assert response.status_code == 404


# --- Business Landing View Tests ---

def test_business_landing_view_get(client):
    """Test business landing view GET displays landing page."""
    url = reverse('accounts_app:business_landing')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'accounts_app/business_landing.html' in [t.name for t in response.templates]


# --- Customer Password Reset View Tests ---

def test_customer_password_reset_view_get(client):
    """Test customer password reset view GET displays form."""
    url = reverse('accounts_app:customer_password_reset')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'registration/password_reset_form.html' in [t.name for t in response.templates]


def test_customer_password_reset_view_post_valid_email(client):
    """Test customer password reset view POST with valid email sends reset email."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    
    url = reverse('accounts_app:customer_password_reset')
    data = {'email': '<EMAIL>'}
    
    with mock.patch('django.core.mail.send_mail') as mock_send_mail:
        response = client.post(url, data=data)
    
    # Should redirect to password reset done
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:customer_password_reset_done')
    
    # Should send reset email
    mock_send_mail.assert_called_once()


def test_customer_password_reset_view_post_invalid_email(client):
    """Test customer password reset view POST with invalid email still redirects."""
    url = reverse('accounts_app:customer_password_reset')
    data = {'email': '<EMAIL>'}
    
    response = client.post(url, data=data)
    
    # Should still redirect to done page (security measure)
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:customer_password_reset_done')


def test_customer_password_reset_view_post_wrong_role(client):
    """Test customer password reset view POST with service provider email still redirects."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    
    url = reverse('accounts_app:customer_password_reset')
    data = {'email': '<EMAIL>'}
    
    response = client.post(url, data=data)
    
    # Should still redirect to done page (security measure)
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:customer_password_reset_done')


def test_customer_password_reset_done_view(client):
    """Test customer password reset done view displays success message."""
    url = reverse('accounts_app:customer_password_reset_done')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'registration/password_reset_done.html' in [t.name for t in response.templates]


def test_customer_password_reset_confirm_view_valid_token(client):
    """Test customer password reset confirm view with valid token displays form."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    
    # Generate valid token
    token = default_token_generator.make_token(user)
    uidb64 = urlsafe_base64_encode(force_bytes(user.pk))
    
    url = reverse('accounts_app:customer_password_reset_confirm', kwargs={
        'uidb64': uidb64,
        'token': token
    })
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'registration/password_reset_confirm.html' in [t.name for t in response.templates]


def test_customer_password_reset_confirm_view_invalid_token(client):
    """Test customer password reset confirm view with invalid token shows error."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    
    # Generate invalid token
    uidb64 = urlsafe_base64_encode(force_bytes(user.pk))
    
    url = reverse('accounts_app:customer_password_reset_confirm', kwargs={
        'uidb64': uidb64,
        'token': 'invalid-token'
    })
    response = client.get(url)
    
    assert response.status_code == 200
    # Should show invalid token message
    assert not response.context['validlink']


def test_customer_password_reset_confirm_view_post_valid(client):
    """Test customer password reset confirm view POST with valid data resets password."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    
    # Generate valid token
    token = default_token_generator.make_token(user)
    uidb64 = urlsafe_base64_encode(force_bytes(user.pk))
    
    url = reverse('accounts_app:customer_password_reset_confirm', kwargs={
        'uidb64': uidb64,
        'token': token
    })
    data = {
        'new_password1': 'NewStrongPass123!',
        'new_password2': 'NewStrongPass123!',
    }
    response = client.post(url, data=data)
    
    # Should redirect to password reset complete
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:customer_password_reset_complete')
    
    # Check password was changed
    user.refresh_from_db()
    assert user.check_password('NewStrongPass123!')


def test_customer_password_reset_complete_view(client):
    """Test customer password reset complete view displays success message."""
    url = reverse('accounts_app:customer_password_reset_complete')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'registration/password_reset_complete.html' in [t.name for t in response.templates]


# --- Service Provider Password Reset View Tests ---

def test_service_provider_password_reset_view_get(client):
    """Test service provider password reset view GET displays form."""
    url = reverse('accounts_app:service_provider_password_reset')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'registration/password_reset_form.html' in [t.name for t in response.templates]


def test_service_provider_password_reset_view_post_valid_email(client):
    """Test service provider password reset view POST with valid email sends reset email."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    
    url = reverse('accounts_app:service_provider_password_reset')
    data = {'email': '<EMAIL>'}
    
    with mock.patch('django.core.mail.send_mail') as mock_send_mail:
        response = client.post(url, data=data)
    
    # Should redirect to password reset done
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_password_reset_done')
    
    # Should send reset email
    mock_send_mail.assert_called_once()


def test_service_provider_password_reset_view_post_invalid_email(client):
    """Test service provider password reset view POST with invalid email still redirects."""
    url = reverse('accounts_app:service_provider_password_reset')
    data = {'email': '<EMAIL>'}
    
    response = client.post(url, data=data)
    
    # Should still redirect to done page (security measure)
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_password_reset_done')


def test_service_provider_password_reset_view_post_wrong_role(client):
    """Test service provider password reset view POST with customer email still redirects."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.CUSTOMER)
    
    url = reverse('accounts_app:service_provider_password_reset')
    data = {'email': '<EMAIL>'}
    
    response = client.post(url, data=data)
    
    # Should still redirect to done page (security measure)
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_password_reset_done')


def test_service_provider_password_reset_done_view(client):
    """Test service provider password reset done view displays success message."""
    url = reverse('accounts_app:service_provider_password_reset_done')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'registration/password_reset_done.html' in [t.name for t in response.templates]


def test_service_provider_password_reset_confirm_view_valid_token(client):
    """Test service provider password reset confirm view with valid token displays form."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    
    # Generate valid token
    token = default_token_generator.make_token(user)
    uidb64 = urlsafe_base64_encode(force_bytes(user.pk))
    
    url = reverse('accounts_app:service_provider_password_reset_confirm', kwargs={
        'uidb64': uidb64,
        'token': token
    })
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'form' in response.context
    assert 'registration/password_reset_confirm.html' in [t.name for t in response.templates]


def test_service_provider_password_reset_confirm_view_invalid_token(client):
    """Test service provider password reset confirm view with invalid token shows error."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    
    # Generate invalid token
    uidb64 = urlsafe_base64_encode(force_bytes(user.pk))
    
    url = reverse('accounts_app:service_provider_password_reset_confirm', kwargs={
        'uidb64': uidb64,
        'token': 'invalid-token'
    })
    response = client.get(url)
    
    assert response.status_code == 200
    # Should show invalid token message
    assert not response.context['validlink']


def test_service_provider_password_reset_confirm_view_post_valid(client):
    """Test service provider password reset confirm view POST with valid data resets password."""
    user = CustomUser.objects.create_user('<EMAIL>', 'P@ssw0rd123', role=CustomUser.SERVICE_PROVIDER)
    
    # Generate valid token
    token = default_token_generator.make_token(user)
    uidb64 = urlsafe_base64_encode(force_bytes(user.pk))
    
    url = reverse('accounts_app:service_provider_password_reset_confirm', kwargs={
        'uidb64': uidb64,
        'token': token
    })
    data = {
        'new_password1': 'NewStrongPass123!',
        'new_password2': 'NewStrongPass123!',
    }
    response = client.post(url, data=data)
    
    # Should redirect to password reset complete
    assert response.status_code == 302
    assert response.url == reverse('accounts_app:service_provider_password_reset_complete')
    
    # Check password was changed
    user.refresh_from_db()
    assert user.check_password('NewStrongPass123!')


def test_service_provider_password_reset_complete_view(client):
    """Test service provider password reset complete view displays success message."""
    url = reverse('accounts_app:service_provider_password_reset_complete')
    response = client.get(url)
    
    assert response.status_code == 200
    assert 'registration/password_reset_complete.html' in [t.name for t in response.templates]


# Mock all the logging functions to prevent exceptions during tests
@pytest.fixture(autouse=True)
def mock_logging_functions(monkeypatch):
    """Mock logging functions to prevent exceptions during tests."""
    def mock_log_function(*args, **kwargs):
        pass
    
    monkeypatch.setattr('accounts_app.views.customer.log_account_lifecycle_event', mock_log_function)
    monkeypatch.setattr('accounts_app.views.customer.log_user_activity', mock_log_function)
    monkeypatch.setattr('accounts_app.views.customer.log_authentication_event', mock_log_function)
    monkeypatch.setattr('accounts_app.views.customer.log_error', mock_log_function)
    monkeypatch.setattr('accounts_app.views.customer.log_profile_change', mock_log_function)
    monkeypatch.setattr('accounts_app.views.provider.log_account_lifecycle_event', mock_log_function)
    monkeypatch.setattr('accounts_app.views.provider.log_user_activity', mock_log_function)
    monkeypatch.setattr('accounts_app.views.provider.log_authentication_event', mock_log_function)
    monkeypatch.setattr('accounts_app.views.provider.log_error', mock_log_function)